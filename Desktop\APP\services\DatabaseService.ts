import * as SQLite from 'expo-sqlite';
import { Personnel, Vehicle, Task, Inspection } from '@/types/database';

export class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;

  async initializeDatabase() {
    this.db = await SQLite.openDatabaseAsync('airfield_ops.db');
    
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS personnel (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        shift TEXT NOT NULL,
        isPresent INTEGER DEFAULT 0,
        contactInfo TEXT,
        createdAt INTEGER DEFAULT (strftime('%s', 'now') * 1000)
      );

      CREATE TABLE IF NOT EXISTS vehicles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        licensePlate TEXT,
        isAvailable INTEGER DEFAULT 1,
        status TEXT DEFAULT 'available',
        assignedTo TEXT,
        lastMaintenance INTEGER,
        createdAt INTEGER DEFAULT (strftime('%s', 'now') * 1000)
      );

      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        assignedPersonnel TEXT,
        assignedVehicle TEXT,
        status TEXT DEFAULT 'pending',
        priority TEXT DEFAULT 'medium',
        createdAt INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        completedAt INTEGER
      );

      CREATE TABLE IF NOT EXISTS inspections (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        scheduledTime INTEGER NOT NULL,
        assignedPersonnel TEXT,
        status TEXT DEFAULT 'scheduled',
        completedAt INTEGER,
        notes TEXT,
        createdAt INTEGER DEFAULT (strftime('%s', 'now') * 1000)
      );
    `);

    // Initialize with sample data
    await this.initializeSampleData();
  }

  private async initializeSampleData() {
    if (!this.db) return;

    // Check if data already exists
    const existingPersonnel = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM personnel');
    if (existingPersonnel && (existingPersonnel as any).count > 0) return;

    // Sample personnel data
    const personnelData = [
      { id: '1', name: 'John Smith', role: 'Checker 1', shift: 'A', isPresent: 1 },
      { id: '2', name: 'Sarah Johnson', role: 'Checker 2', shift: 'A', isPresent: 1 },
      { id: '3', name: 'Mike Wilson', role: 'Ops 1', shift: 'A', isPresent: 1 },
      { id: '4', name: 'Emma Davis', role: 'Ops 2', shift: 'A', isPresent: 0 },
      { id: '5', name: 'Chris Brown', role: 'Ops 3', shift: 'A', isPresent: 1 },
      { id: '6', name: 'Lisa Garcia', role: 'Ops 4', shift: 'A', isPresent: 1 },
      { id: '7', name: 'Tom Anderson', role: 'Ops 5', shift: 'A', isPresent: 1 },
      { id: '8', name: 'Amy Taylor', role: 'Ops 6', shift: 'A', isPresent: 0 },
      { id: '9', name: 'David Miller', role: 'Ops 7', shift: 'A', isPresent: 1 },
      { id: '10', name: 'Jennifer White', role: 'Ops 8', shift: 'A', isPresent: 1 },
      { id: '11', name: 'Robert Martinez', role: 'Ops 9', shift: 'A', isPresent: 1 },
    ];

    // Sample vehicle data
    const vehicleData = [
      { id: '1', name: 'Runway Patrol 1', type: 'Patrol Car', licensePlate: 'AP-001', isAvailable: 1, status: 'available' },
      { id: '2', name: 'Runway Patrol 2', type: 'Patrol Car', licensePlate: 'AP-002', isAvailable: 1, status: 'available' },
      { id: '3', name: 'Wildlife Control', type: 'Utility Vehicle', licensePlate: 'WC-001', isAvailable: 1, status: 'available' },
      { id: '4', name: 'Maintenance Van', type: 'Van', licensePlate: 'MV-001', isAvailable: 0, status: 'maintenance' },
      { id: '5', name: 'Emergency Response', type: 'Emergency Vehicle', licensePlate: 'ER-001', isAvailable: 1, status: 'available' },
      { id: '6', name: 'Ground Support', type: 'Truck', licensePlate: 'GS-001', isAvailable: 1, status: 'available' },
    ];

    // Sample task data
    const taskData = [
      { id: '1', title: 'Runway 09/27 Inspection', description: 'Complete runway surface inspection', assignedPersonnel: 'Mike Wilson', status: 'pending', priority: 'high' },
      { id: '2', title: 'Wildlife Patrol', description: 'Conduct wildlife deterrent patrol', assignedPersonnel: 'Chris Brown', status: 'in_progress', priority: 'medium' },
      { id: '3', title: 'Taxiway Alpha Check', description: 'Check taxiway Alpha for debris', assignedPersonnel: 'Lisa Garcia', status: 'completed', priority: 'low' },
    ];

    // Insert sample data
    for (const person of personnelData) {
      await this.db.runAsync(
        'INSERT INTO personnel (id, name, role, shift, isPresent) VALUES (?, ?, ?, ?, ?)',
        [person.id, person.name, person.role, person.shift, person.isPresent]
      );
    }

    for (const vehicle of vehicleData) {
      await this.db.runAsync(
        'INSERT INTO vehicles (id, name, type, licensePlate, isAvailable, status) VALUES (?, ?, ?, ?, ?, ?)',
        [vehicle.id, vehicle.name, vehicle.type, vehicle.licensePlate, vehicle.isAvailable, vehicle.status]
      );
    }

    for (const task of taskData) {
      await this.db.runAsync(
        'INSERT INTO tasks (id, title, description, assignedPersonnel, status, priority) VALUES (?, ?, ?, ?, ?, ?)',
        [task.id, task.title, task.description, task.assignedPersonnel, task.status, task.priority]
      );
    }
  }

  async getPersonnel(): Promise<Personnel[]> {
    if (!this.db) return [];
    const result = await this.db.getAllAsync('SELECT * FROM personnel ORDER BY role, name');
    return result.map(row => ({ ...row, isPresent: Boolean(row.isPresent) } as Personnel));
  }

  async getVehicles(): Promise<Vehicle[]> {
    if (!this.db) return [];
    const result = await this.db.getAllAsync('SELECT * FROM vehicles ORDER BY name');
    return result.map(row => ({ ...row, isAvailable: Boolean(row.isAvailable) } as Vehicle));
  }

  async getTasks(): Promise<Task[]> {
    if (!this.db) return [];
    const result = await this.db.getAllAsync('SELECT * FROM tasks ORDER BY createdAt DESC');
    return result as Task[];
  }

  async getInspections(): Promise<Inspection[]> {
    if (!this.db) return [];
    const result = await this.db.getAllAsync('SELECT * FROM inspections ORDER BY scheduledTime ASC');
    return result as Inspection[];
  }

  async updatePersonnelAttendance(id: string, isPresent: boolean): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync('UPDATE personnel SET isPresent = ? WHERE id = ?', [isPresent ? 1 : 0, id]);
  }

  async updateVehicleAvailability(id: string, isAvailable: boolean): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync('UPDATE vehicles SET isAvailable = ? WHERE id = ?', [isAvailable ? 1 : 0, id]);
  }

  async assignVehicle(vehicleId: string, personnelId?: string): Promise<void> {
    if (!this.db) return;
    await this.db.runAsync('UPDATE vehicles SET assignedTo = ? WHERE id = ?', [personnelId || null, vehicleId]);
  }

  async updateTaskStatus(id: string, status: string): Promise<void> {
    if (!this.db) return;
    const completedAt = status === 'completed' ? Date.now() : null;
    await this.db.runAsync('UPDATE tasks SET status = ?, completedAt = ? WHERE id = ?', [status, completedAt, id]);
  }

  async createTask(taskData: Omit<Task, 'id' | 'createdAt'>): Promise<void> {
    if (!this.db) return;
    const id = Date.now().toString();
    await this.db.runAsync(
      'INSERT INTO tasks (id, title, description, assignedPersonnel, assignedVehicle, status, priority) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [id, taskData.title, taskData.description || null, taskData.assignedPersonnel || null, taskData.assignedVehicle || null, taskData.status, taskData.priority]
    );
  }

  async updateInspection(id: string, updates: Partial<Inspection>): Promise<void> {
    if (!this.db) return;
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    await this.db.runAsync(`UPDATE inspections SET ${fields} WHERE id = ?`, [...values, id]);
  }

  async scheduleInspection(inspectionData: Omit<Inspection, 'id' | 'createdAt'>): Promise<void> {
    if (!this.db) return;
    const id = Date.now().toString();
    await this.db.runAsync(
      'INSERT INTO inspections (id, type, scheduledTime, assignedPersonnel, status) VALUES (?, ?, ?, ?, ?)',
      [id, inspectionData.type, inspectionData.scheduledTime, inspectionData.assignedPersonnel || null, inspectionData.status]
    );
  }
}