import React, { createContext, useContext, useEffect, useState } from 'react';
import { DatabaseService } from '@/services/DatabaseService';
import { Personnel, Vehicle, Task, Inspection } from '@/types/database';

interface DatabaseContextType {
  db: DatabaseService;
  personnel: Personnel[];
  vehicles: Vehicle[];
  tasks: Task[];
  inspections: Inspection[];
  isLoading: boolean;
  refreshData: () => Promise<void>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

export function DatabaseProvider({ children }: { children: React.ReactNode }) {
  const [db] = useState(() => new DatabaseService());
  const [personnel, setPersonnel] = useState<Personnel[]>([]);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [inspections, setInspections] = useState<Inspection[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshData = async () => {
    try {
      setIsLoading(true);
      const [personnelData, vehicleData, taskData, inspectionData] = await Promise.all([
        db.getPersonnel(),
        db.getVehicles(),
        db.getTasks(),
        db.getInspections(),
      ]);
      
      setPersonnel(personnelData);
      setVehicles(vehicleData);
      setTasks(taskData);
      setInspections(inspectionData);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    db.initializeDatabase().then(() => {
      refreshData();
    });
  }, []);

  return (
    <DatabaseContext.Provider
      value={{
        db,
        personnel,
        vehicles,
        tasks,
        inspections,
        isLoading,
        refreshData,
      }}
    >
      {children}
    </DatabaseContext.Provider>
  );
}

export function useDatabase() {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error('useDatabase must be used within a DatabaseProvider');
  }
  return context;
}