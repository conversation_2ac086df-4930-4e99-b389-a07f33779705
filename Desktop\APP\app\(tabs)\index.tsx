import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ImageBackground } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Clock, Users, Truck, CircleAlert as AlertCircle, CircleCheck as CheckCircle, Calendar, Plane, Shield, Activity } from 'lucide-react-native';
import { useDatabase } from '@/context/DatabaseContext';
import { useNotifications } from '@/context/NotificationContext';
import { getCurrentShift, getTimeUntilNextShift } from '@/utils/shiftUtils';
import { ShiftSelector } from '@/components/ShiftSelector';
import { TaskCard } from '@/components/TaskCard';
import { StatsCard } from '@/components/StatsCard';
import { HeroSection } from '@/components/HeroSection';

export default function DashboardScreen() {
  const { db, personnel, vehicles, tasks, inspections } = useDatabase();
  const { scheduleInspectionReminder } = useNotifications();
  const [currentShift, setCurrentShift] = useState(getCurrentShift());
  const [nextShiftTime, setNextShiftTime] = useState(getTimeUntilNextShift());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentShift(getCurrentShift());
      setNextShiftTime(getTimeUntilNextShift());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  const activePersonnel = personnel.filter(p => p.isPresent && p.shift === currentShift).length;
  const availableVehicles = vehicles.filter(v => v.isAvailable).length;
  const pendingTasks = tasks.filter(t => t.status === 'pending').length;
  const completedTasks = tasks.filter(t => t.status === 'completed').length;

  const upcomingInspections = inspections
    .filter(i => i.scheduledTime > Date.now() && i.scheduledTime < Date.now() + 3600000)
    .sort((a, b) => a.scheduledTime - b.scheduledTime);

  const handleStartInspection = async (inspectionId: string) => {
    try {
      await db.updateInspection(inspectionId, { status: 'in_progress' });
      Alert.alert('Success', 'Inspection started successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to start inspection');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <HeroSection 
          currentShift={currentShift}
          nextShiftTime={nextShiftTime}
          activePersonnel={activePersonnel}
          availableVehicles={availableVehicles}
          pendingTasks={pendingTasks}
        />



        <View style={styles.statsGrid}>
          <StatsCard
            icon={<Users size={24} color="#1e3a8a" />}
            title="Personnel"
            value={activePersonnel}
            subtitle="on duty"
            color="#1e3a8a"
          />
          <StatsCard
            icon={<Truck size={24} color="#059669" />}
            title="Vehicles"
            value={availableVehicles}
            subtitle="available"
            color="#059669"
          />
          <StatsCard
            icon={<Clock size={24} color="#dc2626" />}
            title="Pending"
            value={pendingTasks}
            subtitle="tasks"
            color="#dc2626"
          />
          <StatsCard
            icon={<CheckCircle size={24} color="#7c3aed" />}
            title="Completed"
            value={completedTasks}
            subtitle="today"
            color="#7c3aed"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Upcoming Inspections</Text>
          {upcomingInspections.length > 0 ? (
            upcomingInspections.map((inspection) => (
              <TaskCard
                key={inspection.id}
                title={inspection.type}
                subtitle={`${inspection.assignedPersonnel?.join(', ') || 'Unassigned'}`}
                time={new Date(inspection.scheduledTime).toLocaleTimeString()}
                status={inspection.status}
                priority="high"
                onPress={() => handleStartInspection(inspection.id)}
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <Calendar size={48} color="#6b7280" />
              <Text style={styles.emptyStateText}>No upcoming inspections</Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Tasks</Text>
          {tasks.slice(0, 3).map((task) => (
            <TaskCard
              key={task.id}
              title={task.title}
              subtitle={task.assignedPersonnel || 'Unassigned'}
              time={new Date(task.createdAt).toLocaleTimeString()}
              status={task.status}
              priority={task.priority}
              onPress={() => {}}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    gap: 16,
  },
  section: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 12,
  },
});