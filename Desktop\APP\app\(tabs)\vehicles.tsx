import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, StatusBar } from 'react-native';
import { Truck, Car, Wrench, CircleCheck as CheckCircle, Circle as XCircle } from 'lucide-react-native';
import { useDatabase } from '@/context/DatabaseContext';
import { VehicleCard } from '@/components/VehicleCard';

export default function VehiclesScreen() {
  const { db, vehicles } = useDatabase();
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  const filteredVehicles = selectedFilter === 'all' 
    ? vehicles 
    : vehicles.filter(v => v.status === selectedFilter);

  const availableCount = vehicles.filter(v => v.isAvailable).length;
  const assignedCount = vehicles.filter(v => v.assignedTo).length;
  const maintenanceCount = vehicles.filter(v => v.status === 'maintenance').length;

  const handleVehicleToggle = async (vehicleId: string, isAvailable: boolean) => {
    try {
      await db.updateVehicleAvailability(vehicleId, !isAvailable);
      Alert.alert('Success', `Vehicle availability updated`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update vehicle');
    }
  };

  const handleAssignVehicle = async (vehicleId: string, personnelId?: string) => {
    try {
      await db.assignVehicle(vehicleId, personnelId);
      Alert.alert('Success', personnelId ? 'Vehicle assigned' : 'Vehicle unassigned');
    } catch (error) {
      Alert.alert('Error', 'Failed to assign vehicle');
    }
  };

  const filters = [
    { id: 'all', label: 'All', count: vehicles.length },
    { id: 'available', label: 'Available', count: availableCount },
    { id: 'assigned', label: 'Assigned', count: assignedCount },
    { id: 'maintenance', label: 'Maintenance', count: maintenanceCount },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent={true} />
      <View style={styles.header}>
        <Text style={styles.title}>Vehicle Management</Text>
        <Text style={styles.subtitle}>{availableCount} Available • {assignedCount} Assigned</Text>
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterButton,
              selectedFilter === filter.id && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(filter.id)}
          >
            <Text style={[
              styles.filterText,
              selectedFilter === filter.id && styles.filterTextActive
            ]}>
              {filter.label} ({filter.count})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Fleet Status</Text>
          {filteredVehicles.map((vehicle) => (
            <VehicleCard
              key={vehicle.id}
              vehicle={vehicle}
              onToggleAvailability={handleVehicleToggle}
              onAssign={handleAssignVehicle}
            />
          ))}
        </View>

        {filteredVehicles.length === 0 && (
          <View style={styles.emptyState}>
            <Truck size={48} color="#6b7280" />
            <Text style={styles.emptyStateText}>No vehicles found</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#1e3a8a',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    color: '#93c5fd',
    marginTop: 4,
  },
  filterContainer: {
    padding: 20,
    paddingBottom: 0,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#e5e7eb',
    marginRight: 12,
    minWidth: 80,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#1e3a8a',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  filterTextActive: {
    color: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 12,
  },
});