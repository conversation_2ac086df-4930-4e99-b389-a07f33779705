import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Truck, Car, Wrench, User, CircleCheck as CheckCircle, Circle as XCircle } from 'lucide-react-native';
import { Vehicle } from '@/types/database';

interface VehicleCardProps {
  vehicle: Vehicle;
  onToggleAvailability: (id: string, isAvailable: boolean) => void;
  onAssign: (id: string, personnelId?: string) => void;
}

export function VehicleCard({ vehicle, onToggleAvailability, onAssign }: VehicleCardProps) {
  const getVehicleIcon = () => {
    switch (vehicle.type) {
      case 'Truck': return <Truck size={20} color="#1e3a8a" />;
      case 'Emergency Vehicle': return <Car size={20} color="#dc2626" />;
      default: return <Car size={20} color="#6b7280" />;
    }
  };

  const getStatusColor = () => {
    switch (vehicle.status) {
      case 'available': return '#10b981';
      case 'assigned': return '#f59e0b';
      case 'maintenance': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = () => {
    switch (vehicle.status) {
      case 'available': return <CheckCircle size={16} color="#ffffff" />;
      case 'assigned': return <User size={16} color="#ffffff" />;
      case 'maintenance': return <Wrench size={16} color="#ffffff" />;
      default: return <XCircle size={16} color="#ffffff" />;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.info}>
          <View style={styles.nameContainer}>
            <View style={styles.iconContainer}>
              {getVehicleIcon()}
            </View>
            <View>
              <Text style={styles.name}>{vehicle.name}</Text>
              <Text style={styles.type}>{vehicle.type}</Text>
              {vehicle.licensePlate && (
                <Text style={styles.licensePlate}>{vehicle.licensePlate}</Text>
              )}
            </View>
          </View>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor() }
            ]}>
              {getStatusIcon()}
            </View>
            <Text style={[
              styles.statusText,
              { color: getStatusColor() }
            ]}>
              {vehicle.status.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      {vehicle.assignedTo && (
        <View style={styles.assignmentInfo}>
          <User size={14} color="#6b7280" />
          <Text style={styles.assignedText}>Assigned to: {vehicle.assignedTo}</Text>
        </View>
      )}
      
      <View style={styles.actions}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            vehicle.isAvailable ? styles.actionButtonUnavailable : styles.actionButtonAvailable
          ]}
          onPress={() => onToggleAvailability(vehicle.id, vehicle.isAvailable)}
        >
          <Text style={[
            styles.actionButtonText,
            vehicle.isAvailable ? styles.actionButtonTextUnavailable : styles.actionButtonTextAvailable
          ]}>
            Mark {vehicle.isAvailable ? 'Unavailable' : 'Available'}
          </Text>
        </TouchableOpacity>
        
        {vehicle.isAvailable && (
          <TouchableOpacity
            style={styles.assignButton}
            onPress={() => onAssign(vehicle.id, vehicle.assignedTo ? undefined : 'placeholder')}
          >
            <Text style={styles.assignButtonText}>
              {vehicle.assignedTo ? 'Unassign' : 'Assign'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    marginBottom: 12,
  },
  info: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  type: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  licensePlate: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 2,
    fontFamily: 'monospace',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  assignmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    padding: 8,
    backgroundColor: '#f8fafc',
    borderRadius: 6,
  },
  assignedText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 6,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
  },
  actionButtonAvailable: {
    backgroundColor: '#f0fdf4',
    borderColor: '#86efac',
  },
  actionButtonUnavailable: {
    backgroundColor: '#fef2f2',
    borderColor: '#fca5a5',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButtonTextAvailable: {
    color: '#16a34a',
  },
  actionButtonTextUnavailable: {
    color: '#dc2626',
  },
  assignButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: '#1e3a8a',
  },
  assignButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
});