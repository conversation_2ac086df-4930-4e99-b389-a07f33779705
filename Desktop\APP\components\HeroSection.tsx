import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Plane, Shield, Clock, Users, Truck, MapPin, Radio, Calendar } from 'lucide-react-native';

interface HeroSectionProps {
  currentShift: string;
  nextShiftTime: string;
  activePersonnel: number;
  availableVehicles: number;
  pendingTasks: number;
}

export function HeroSection({
  currentShift,
  nextShiftTime,
  activePersonnel,
  availableVehicles,
  pendingTasks
}: HeroSectionProps) {
  const getCurrentDate = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <View style={styles.container}>
      <Image
        source={{ uri: 'https://images.pexels.com/photos/912050/pexels-photo-912050.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2' }}
        style={styles.backgroundImage}
      />
      
      <LinearGradient
        colors={['rgba(15, 23, 42, 0.95)', 'rgba(30, 58, 138, 0.85)', 'rgba(59, 130, 246, 0.75)']}
        style={styles.overlay}
      >
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <View style={styles.iconContainer}>
                <Plane size={28} color="#ffffff" />
              </View>
              <View style={styles.titleText}>
                <Text style={styles.title}>Airfield Operations</Text>
                <Text style={styles.subtitle}>Command & Control Center</Text>
              </View>
            </View>
            
            <View style={styles.statusContainer}>
              <View style={[styles.statusIndicator, { backgroundColor: '#10b981' }]}>
                <Shield size={14} color="#ffffff" />
              </View>
              <Text style={styles.statusText}>Active</Text>
            </View>
          </View>

          {/* Shift Information */}
          <View style={styles.shiftInfo}>
            <View style={styles.shiftCard}>
              <Text style={styles.shiftLabel}>Current Shift</Text>
              <Text style={styles.shiftValue}>Shift {currentShift}</Text>
              <View style={styles.shiftIndicator}>
                <View style={styles.shiftDot} />
                <Text style={styles.shiftStatus}>Active</Text>
              </View>
            </View>
            <View style={styles.timeCard}>
              <Calendar size={18} color="#93c5fd" />
              <View style={styles.timeInfo}>
                <Text style={styles.timeLabel}>Date</Text>
                <Text style={styles.timeValue}>{getCurrentDate()}</Text>
              </View>
            </View>
          </View>

          {/* Enhanced Stats Grid */}
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <Users size={20} color="#3b82f6" />
              </View>
              <Text style={styles.statValue}>{activePersonnel}</Text>
              <Text style={styles.statLabel}>Personnel</Text>
            </View>
            
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <Truck size={20} color="#10b981" />
              </View>
              <Text style={styles.statValue}>{availableVehicles}</Text>
              <Text style={styles.statLabel}>Vehicles</Text>
            </View>
            
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <Clock size={20} color="#f59e0b" />
              </View>
              <Text style={styles.statValue}>{pendingTasks}</Text>
              <Text style={styles.statLabel}>Pending</Text>
            </View>
          </View>

          {/* Weather & Conditions */}
          <View style={styles.conditionsBar}>
            <View style={styles.conditionItem}>
              <Text style={styles.conditionLabel}>Visibility</Text>
              <Text style={styles.conditionValue}>10+ km</Text>
            </View>
            <View style={styles.conditionDivider} />
            <View style={styles.conditionItem}>
              <Text style={styles.conditionLabel}>Wind</Text>
              <Text style={styles.conditionValue}>5 kts</Text>
            </View>
            <View style={styles.conditionDivider} />
            <View style={styles.conditionItem}>
              <Text style={styles.conditionLabel}>Ceiling</Text>
              <Text style={styles.conditionValue}>Clear</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 320,
    position: 'relative',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
  },
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    padding: 20,
    paddingTop: 40,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    padding: 8,
    borderRadius: 12,
    marginRight: 12,
  },
  titleText: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 13,
    color: '#93c5fd',
    marginTop: 2,
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  statusIndicator: {
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  statusText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#ffffff',
  },
  shiftInfo: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  shiftCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.12)',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  shiftLabel: {
    fontSize: 11,
    color: '#93c5fd',
    marginBottom: 4,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  shiftValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 8,
  },
  shiftIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shiftDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#10b981',
    marginRight: 6,
  },
  shiftStatus: {
    fontSize: 11,
    color: '#10b981',
    fontWeight: '600',
  },
  timeCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.12)',
    padding: 16,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  timeInfo: {
    marginLeft: 12,
  },
  timeLabel: {
    fontSize: 11,
    color: '#93c5fd',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timeValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    marginTop: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    padding: 14,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
  },
  statIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 8,
    borderRadius: 10,
    marginBottom: 8,
  },
  statValue: {
    fontSize: 22,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#e2e8f0',
    fontWeight: '600',
  },
  conditionsBar: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'space-around',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  conditionItem: {
    alignItems: 'center',
    flex: 1,
  },
  conditionLabel: {
    fontSize: 10,
    color: '#93c5fd',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  conditionValue: {
    fontSize: 13,
    color: '#ffffff',
    fontWeight: '600',
    marginTop: 2,
  },
  conditionDivider: {
    width: 1,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
});