import React, { createContext, useContext, useEffect, useState } from 'react';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

interface NotificationContextType {
  scheduleInspectionReminder: (inspectionId: string, time: number) => Promise<void>;
  cancelNotification: (notificationId: string) => Promise<void>;
  permissionStatus: Notifications.PermissionStatus | null;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [permissionStatus, setPermissionStatus] = useState<Notifications.PermissionStatus | null>(null);

  useEffect(() => {
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    if (Platform.OS === 'web') {
      setPermissionStatus({ granted: false, canAskAgain: false, status: 'denied' });
      return;
    }

    const { status } = await Notifications.requestPermissionsAsync();
    setPermissionStatus({ granted: status === 'granted', canAskAgain: true, status });
  };

  const scheduleInspectionReminder = async (inspectionId: string, time: number) => {
    if (Platform.OS === 'web' || !permissionStatus?.granted) {
      return;
    }

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Inspection Reminder',
          body: 'Scheduled airfield inspection starting soon',
          data: { inspectionId },
        },
        trigger: {
          date: new Date(time - 15 * 60 * 1000), // 15 minutes before
        },
      });
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  };

  const cancelNotification = async (notificationId: string) => {
    if (Platform.OS === 'web') {
      return;
    }

    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        scheduleInspectionReminder,
        cancelNotification,
        permissionStatus,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}