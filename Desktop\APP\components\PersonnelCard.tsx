import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { User, Shield, CircleCheck as CheckCircle, Circle as XCircle } from 'lucide-react-native';
import { Personnel } from '@/types/database';

interface PersonnelCardProps {
  person: Personnel;
  onAttendanceToggle: (id: string, isPresent: boolean) => void;
}

export function PersonnelCard({ person, onAttendanceToggle }: PersonnelCardProps) {
  const isChecker = person.role.includes('Checker');
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.info}>
          <View style={styles.nameContainer}>
            <View style={styles.iconContainer}>
              {isChecker ? (
                <Shield size={20} color="#1e3a8a" />
              ) : (
                <User size={20} color="#6b7280" />
              )}
            </View>
            <View>
              <Text style={styles.name}>{person.name}</Text>
              <Text style={styles.role}>{person.role}</Text>
            </View>
          </View>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: person.isPresent ? '#10b981' : '#ef4444' }
            ]}>
              {person.isPresent ? (
                <CheckCircle size={16} color="#ffffff" />
              ) : (
                <XCircle size={16} color="#ffffff" />
              )}
            </View>
            <Text style={[
              styles.statusText,
              { color: person.isPresent ? '#10b981' : '#ef4444' }
            ]}>
              {person.isPresent ? 'Present' : 'Absent'}
            </Text>
          </View>
        </View>
      </View>
      
      <TouchableOpacity
        style={[
          styles.attendanceButton,
          person.isPresent ? styles.attendanceButtonPresent : styles.attendanceButtonAbsent
        ]}
        onPress={() => onAttendanceToggle(person.id, person.isPresent)}
      >
        <Text style={[
          styles.attendanceButtonText,
          person.isPresent ? styles.attendanceButtonTextPresent : styles.attendanceButtonTextAbsent
        ]}>
          Mark {person.isPresent ? 'Absent' : 'Present'}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    marginBottom: 12,
  },
  info: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  role: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  attendanceButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
  },
  attendanceButtonPresent: {
    backgroundColor: '#fef2f2',
    borderColor: '#fca5a5',
  },
  attendanceButtonAbsent: {
    backgroundColor: '#f0fdf4',
    borderColor: '#86efac',
  },
  attendanceButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  attendanceButtonTextPresent: {
    color: '#dc2626',
  },
  attendanceButtonTextAbsent: {
    color: '#16a34a',
  },
});