import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ShiftType } from '@/utils/shiftUtils';

interface ShiftSelectorProps {
  currentShift: ShiftType;
  onShiftChange: (shift: ShiftType) => void;
}

export function ShiftSelector({ currentShift, onShiftChange }: ShiftSelectorProps) {
  const shifts: ShiftType[] = ['A', 'B', 'C', 'D'];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Active Shift</Text>
      <View style={styles.shiftButtons}>
        {shifts.map((shift) => (
          <TouchableOpacity
            key={shift}
            style={[
              styles.shiftButton,
              currentShift === shift && styles.shiftButtonActive
            ]}
            onPress={() => onShiftChange(shift)}
          >
            <Text style={[
              styles.shiftButtonText,
              currentShift === shift && styles.shiftButtonTextActive
            ]}>
              {shift}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  shiftButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  shiftButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    alignItems: 'center',
  },
  shiftButtonActive: {
    backgroundColor: '#1e3a8a',
  },
  shiftButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6b7280',
  },
  shiftButtonTextActive: {
    color: '#ffffff',
  },
});