import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Users, UserCheck, UserX, Shield, User } from 'lucide-react-native';
import { useDatabase } from '@/context/DatabaseContext';
import { PersonnelCard } from '@/components/PersonnelCard';
import { getCurrentShift } from '@/utils/shiftUtils';

export default function PersonnelScreen() {
  const { db, personnel } = useDatabase();
  const [currentShift, setCurrentShift] = useState(getCurrentShift());
  const [selectedRole, setSelectedRole] = useState<string>('all');

  const shiftPersonnel = personnel.filter(p => p.shift === currentShift);
  const filteredPersonnel = selectedRole === 'all' 
    ? shiftPersonnel 
    : shiftPersonnel.filter(p => p.role === selectedRole);

  const checkers = shiftPersonnel.filter(p => p.role.includes('Checker'));
  const operators = shiftPersonnel.filter(p => p.role.includes('Ops'));
  const presentCount = shiftPersonnel.filter(p => p.isPresent).length;

  const handleAttendanceToggle = async (personnelId: string, isPresent: boolean) => {
    try {
      await db.updatePersonnelAttendance(personnelId, !isPresent);
      Alert.alert('Success', `Attendance updated successfully`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update attendance');
    }
  };

  const roleFilters = [
    { id: 'all', label: 'All', count: shiftPersonnel.length },
    { id: 'Checker', label: 'Checkers', count: checkers.length },
    { id: 'Ops', label: 'Operators', count: operators.length },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Personnel Management</Text>
        <Text style={styles.subtitle}>Shift {currentShift} • {presentCount}/{shiftPersonnel.length} Present</Text>
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
        {roleFilters.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterButton,
              selectedRole === filter.id && styles.filterButtonActive
            ]}
            onPress={() => setSelectedRole(filter.id)}
          >
            <Text style={[
              styles.filterText,
              selectedRole === filter.id && styles.filterTextActive
            ]}>
              {filter.label} ({filter.count})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shift Personnel</Text>
          {filteredPersonnel.map((person) => (
            <PersonnelCard
              key={person.id}
              person={person}
              onAttendanceToggle={handleAttendanceToggle}
            />
          ))}
        </View>

        {filteredPersonnel.length === 0 && (
          <View style={styles.emptyState}>
            <Users size={48} color="#6b7280" />
            <Text style={styles.emptyStateText}>No personnel found</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#1e3a8a',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    color: '#93c5fd',
    marginTop: 4,
  },
  filterContainer: {
    padding: 20,
    paddingBottom: 0,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#e5e7eb',
    marginRight: 12,
    minWidth: 80,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#1e3a8a',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  filterTextActive: {
    color: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 12,
  },
});