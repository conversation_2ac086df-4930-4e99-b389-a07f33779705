export type ShiftType = 'A' | 'B' | 'C' | 'D';

export function getCurrentShift(): ShiftType {
  const now = new Date();
  const hours = now.getHours();
  
  // Shift A: 06:00 - 18:00
  // Shift B: 18:00 - 06:00
  // Shift C: 06:00 - 18:00 (alternate day)
  // Shift D: 18:00 - 06:00 (alternate day)
  
  if (hours >= 6 && hours < 18) {
    return 'A';
  } else {
    return 'B';
  }
}

export function getTimeUntilNextShift(): string {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  
  let nextShiftHour: number;
  
  if (hours >= 6 && hours < 18) {
    nextShiftHour = 18; // Next shift at 18:00
  } else {
    nextShiftHour = 6; // Next shift at 06:00
  }
  
  const currentTime = hours * 60 + minutes;
  let nextShiftTime = nextShiftHour * 60;
  
  if (nextShiftTime <= currentTime) {
    nextShiftTime += 24 * 60; // Add 24 hours if next shift is tomorrow
  }
  
  const timeDifference = nextShiftTime - currentTime;
  const hoursLeft = Math.floor(timeDifference / 60);
  const minutesLeft = timeDifference % 60;
  
  return `${hoursLeft}h ${minutesLeft}m remaining`;
}

export function getShiftPersonnel(shift: ShiftType): string[] {
  const basePersonnel = [
    'Checker 1', 'Checker 2',
    'Ops 1', 'Ops 2', 'Ops 3', 'Ops 4', 'Ops 5',
    'Ops 6', 'Ops 7', 'Ops 8', 'Ops 9'
  ];
  
  return basePersonnel;
}

export function calculateNextInspectionTime(lastInspectionTime: number): number {
  return lastInspectionTime + (1.5 * 60 * 60 * 1000); // Add 1.5 hours
}

export function generateInspectionPairs(availablePersonnel: string[]): string[][] {
  const pairs: string[][] = [];
  
  for (let i = 0; i < availablePersonnel.length; i += 2) {
    if (i + 1 < availablePersonnel.length) {
      pairs.push([availablePersonnel[i], availablePersonnel[i + 1]]);
    }
  }
  
  return pairs;
}