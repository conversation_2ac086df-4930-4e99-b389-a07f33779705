export interface Personnel {
  id: string;
  name: string;
  role: string;
  shift: string;
  isPresent: boolean;
  contactInfo?: string;
  createdAt: number;
}

export interface Vehicle {
  id: string;
  name: string;
  type: string;
  licensePlate?: string;
  isAvailable: boolean;
  status: 'available' | 'assigned' | 'maintenance';
  assignedTo?: string;
  lastMaintenance?: number;
  createdAt: number;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  assignedPersonnel?: string;
  assignedVehicle?: string;
  status: 'pending' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  completedAt?: number;
}

export interface Inspection {
  id: string;
  type: string;
  scheduledTime: number;
  assignedPersonnel?: string;
  status: 'scheduled' | 'in_progress' | 'completed';
  completedAt?: number;
  notes?: string;
  createdAt: number;
}