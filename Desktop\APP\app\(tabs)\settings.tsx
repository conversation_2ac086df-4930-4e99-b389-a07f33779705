import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, StatusBar } from 'react-native';
import { Settings, Bell, Clock, Users, Truck, Shield, Info } from 'lucide-react-native';

export default function SettingsScreen() {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoScheduleEnabled, setAutoScheduleEnabled] = useState(true);
  const [twentyFourHourFormat, setTwentyFourHourFormat] = useState(false);

  const settingsData = [
    {
      title: 'Notifications',
      icon: <Bell size={24} color="#1e3a8a" />,
      subtitle: 'Push notifications for tasks and inspections',
      value: notificationsEnabled,
      onToggle: setNotificationsEnabled,
    },
    {
      title: 'Auto Schedule',
      icon: <Clock size={24} color="#1e3a8a" />,
      subtitle: 'Automatically schedule inspections every 1.5 hours',
      value: autoScheduleEnabled,
      onToggle: setAutoScheduleEnabled,
    },
    {
      title: '24-Hour Format',
      icon: <Clock size={24} color="#1e3a8a" />,
      subtitle: 'Use 24-hour time format',
      value: twentyFourHourFormat,
      onToggle: setTwentyFourHourFormat,
    },
  ];

  const informationData = [
    {
      title: 'Shift Information',
      icon: <Users size={24} color="#1e3a8a" />,
      subtitle: 'View shift schedules and personnel roles',
    },
    {
      title: 'Vehicle Fleet',
      icon: <Truck size={24} color="#1e3a8a" />,
      subtitle: 'Fleet management and maintenance schedules',
    },
    {
      title: 'Security & Access',
      icon: <Shield size={24} color="#1e3a8a" />,
      subtitle: 'Role-based access control settings',
    },
    {
      title: 'About',
      icon: <Info size={24} color="#1e3a8a" />,
      subtitle: 'App version 1.0.0 - Airfield Operations',
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent={true} />
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>Configure app preferences</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          {settingsData.map((setting, index) => (
            <View key={index} style={styles.settingCard}>
              <View style={styles.settingContent}>
                <View style={styles.settingIcon}>
                  {setting.icon}
                </View>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>{setting.title}</Text>
                  <Text style={styles.settingSubtitle}>{setting.subtitle}</Text>
                </View>
              </View>
              <Switch
                value={setting.value}
                onValueChange={setting.onToggle}
                trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
                thumbColor={setting.value ? '#ffffff' : '#f4f4f5'}
              />
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Information</Text>
          {informationData.map((item, index) => (
            <TouchableOpacity key={index} style={styles.infoCard}>
              <View style={styles.infoIcon}>
                {item.icon}
              </View>
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>{item.title}</Text>
                <Text style={styles.infoSubtitle}>{item.subtitle}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>System Information</Text>
          <View style={styles.systemCard}>
            <Text style={styles.systemTitle}>Airfield Operations System</Text>
            <Text style={styles.systemDescription}>
              Professional task and vehicle distribution system for airfield operations.
              Manage shifts, personnel, vehicles, and scheduled inspections.
            </Text>
            <View style={styles.systemStats}>
              <View style={styles.systemStat}>
                <Text style={styles.systemStatValue}>4</Text>
                <Text style={styles.systemStatLabel}>Shifts</Text>
              </View>
              <View style={styles.systemStat}>
                <Text style={styles.systemStatValue}>11</Text>
                <Text style={styles.systemStatLabel}>Personnel</Text>
              </View>
              <View style={styles.systemStat}>
                <Text style={styles.systemStatValue}>1.5h</Text>
                <Text style={styles.systemStatLabel}>Inspection Interval</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#1e3a8a',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    color: '#93c5fd',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  settingCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  infoCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  infoIcon: {
    marginRight: 12,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  infoSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  systemCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  systemTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1f2937',
    marginBottom: 8,
  },
  systemDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  systemStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  systemStat: {
    alignItems: 'center',
  },
  systemStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e3a8a',
  },
  systemStatLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
});