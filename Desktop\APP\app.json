{"expo": {"name": "Airfield Operations", "slug": "airfield-operations", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "airfield-ops", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#1e3a8a"}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", "expo-notifications", "expo-sqlite"], "experiments": {"typedRoutes": true}}}